#!/usr/bin/env python3
"""
Demo of the clean Instagram scraper with exact fields requested
"""

from instagram_scraper_advanced import scrape_instagram_advanced, save_to_csv
import pandas as pd

def demo_clean_scraper():
    """Demo the clean scraper with specific fields"""
    print("🧹 Clean Instagram Scraper Demo")
    print("=" * 40)
    print("📋 This scraper extracts ONLY:")
    print("   • Caption content")
    print("   • Post date")
    print("   • Number of likes")
    print("   • Image description")
    print()
    
    username = input("Enter Instagram username: ").strip()
    
    # Ask for scraping options
    scrape_all = input("Scrape ALL posts? (y/n): ").strip().lower() == 'y'
    
    if not scrape_all:
        try:
            max_posts = int(input("How many posts? (default 5): ").strip() or "5")
        except:
            max_posts = 5
    else:
        max_posts = 0
        print("⚠️  Scraping ALL posts may take 10-30 minutes!")
        confirm = input("Continue? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Cancelled")
            return
    
    print(f"\n🚀 Starting scrape...")
    
    # Scrape the profile
    result = scrape_instagram_advanced(username, max_posts, scrape_all)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    # Save clean CSV
    csv_file = save_to_csv(result)
    
    if csv_file:
        # Show preview of CSV
        try:
            df = pd.read_csv(csv_file)
            print(f"\n📊 CSV Preview:")
            print("=" * 50)
            print(df.to_string(index=False, max_colwidth=50))
            
            print(f"\n📈 Summary:")
            print(f"   • Total posts: {len(df)}")
            print(f"   • Date range: {df['Date'].min()} to {df['Date'].max()}")
            print(f"   • Total likes: {df['Likes'].astype(str).str.extract('(\d+)').astype(int).sum().iloc[0]}")
            print(f"   • Posts with captions: {len(df[df['Caption'] != ''])}")
            print(f"   • Posts with image descriptions: {len(df[df['Image_Description'] != ''])}")
            
        except Exception as e:
            print(f"❌ Error reading CSV: {e}")

def show_csv_structure():
    """Show the clean CSV structure"""
    print("📋 Clean CSV Structure:")
    print("=" * 30)
    print("Column 1: Post_Number - Sequential number (1, 2, 3...)")
    print("Column 2: Caption - The actual text content of the post")
    print("Column 3: Date - When the post was published (YYYY-MM-DD HH:MM:SS)")
    print("Column 4: Likes - Number of likes (clean number only)")
    print("Column 5: Image_Description - Description of the image/video content")
    print()
    print("✅ Removed fields (as requested):")
    print("   ❌ Profile followers")
    print("   ❌ Profile following")
    print("   ❌ Profile posts count")
    print("   ❌ Profile is private")
    print("   ❌ Profile is verified")
    print()

if __name__ == "__main__":
    show_csv_structure()
    demo_clean_scraper()
