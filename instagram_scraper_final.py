#!/usr/bin/env python3
"""
Instagram Public Profile Scraper - Final Version
Combines multiple approaches for maximum success rate
"""

import instaloader
import requests
import json
import time
import random
from datetime import datetime
import re

def scrape_with_instaloader(username, max_posts=5):
    """
    Method 1: Using instaloader (more detailed but may be blocked)
    """
    print("🔧 Method 1: Using Instaloader...")
    
    try:
        L = instaloader.Instaloader(
            download_pictures=False,
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False,
            request_timeout=15,
            max_connection_attempts=2
        )
        
        # Set user agent
        L.context._session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Load profile
        profile = instaloader.Profile.from_username(L.context, username)
        
        if profile.is_private:
            return {"error": "Profile is private", "method": "instaloader"}
        
        # Get profile info
        profile_data = {
            "method": "instaloader",
            "username": profile.username,
            "full_name": profile.full_name,
            "biography": profile.biography,
            "followers": profile.followers,
            "following": profile.followees,
            "posts_count": profile.mediacount,
            "is_private": profile.is_private,
            "is_verified": profile.is_verified,
            "external_url": profile.external_url,
            "posts": []
        }
        
        # Try to get posts (this might fail due to rate limiting)
        try:
            print(f"📸 Attempting to get {max_posts} posts...")
            post_count = 0
            for post in profile.get_posts():
                if post_count >= max_posts:
                    break
                
                post_data = {
                    "shortcode": post.shortcode,
                    "url": f"https://www.instagram.com/p/{post.shortcode}/",
                    "date": post.date.strftime("%Y-%m-%d %H:%M:%S"),
                    "caption": post.caption if post.caption else "No caption",
                    "type": "Video" if post.is_video else "Photo"
                }
                
                profile_data["posts"].append(post_data)
                post_count += 1
                
                # Add delay between posts
                time.sleep(random.uniform(1, 2))
                
        except Exception as post_error:
            print(f"⚠️  Could not get posts: {post_error}")
            profile_data["posts_error"] = str(post_error)
        
        return profile_data
        
    except Exception as e:
        return {"error": str(e), "method": "instaloader"}

def scrape_with_requests(username):
    """
    Method 2: Using direct HTTP requests (more reliable but limited data)
    """
    print("🔧 Method 2: Using HTTP requests...")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        url = f"https://www.instagram.com/{username}/"
        response = session.get(url, timeout=10)
        
        if response.status_code == 404:
            return {"error": "Profile not found", "method": "requests"}
        elif response.status_code != 200:
            return {"error": f"HTTP {response.status_code}", "method": "requests"}
        
        html = response.text
        
        # Extract data from HTML
        profile_data = {
            "method": "requests",
            "username": username,
            "full_name": "N/A",
            "biography": "N/A",
            "followers": "N/A",
            "following": "N/A",
            "posts_count": "N/A",
            "is_private": False,
            "is_verified": False,
            "external_url": "N/A"
        }
        
        # Try to extract JSON data
        json_pattern = r'window\._sharedData\s*=\s*({.*?});'
        match = re.search(json_pattern, html)
        
        if match:
            try:
                shared_data = json.loads(match.group(1))
                user_data = shared_data.get('entry_data', {}).get('ProfilePage', [{}])[0].get('graphql', {}).get('user', {})
                
                if user_data:
                    profile_data.update({
                        'full_name': user_data.get('full_name', 'N/A'),
                        'biography': user_data.get('biography', 'N/A'),
                        'followers': user_data.get('edge_followed_by', {}).get('count', 'N/A'),
                        'following': user_data.get('edge_follow', {}).get('count', 'N/A'),
                        'posts_count': user_data.get('edge_owner_to_timeline_media', {}).get('count', 'N/A'),
                        'is_private': user_data.get('is_private', False),
                        'is_verified': user_data.get('is_verified', False),
                        'external_url': user_data.get('external_url', 'N/A')
                    })
            except json.JSONDecodeError:
                pass
        
        # Fallback: regex extraction
        patterns = {
            'followers': r'"edge_followed_by":{"count":(\d+)}',
            'following': r'"edge_follow":{"count":(\d+)}',
            'posts_count': r'"edge_owner_to_timeline_media":{"count":(\d+)}',
            'full_name': r'"full_name":"([^"]*)"',
            'biography': r'"biography":"([^"]*)"',
            'is_private': r'"is_private":(true|false)',
            'is_verified': r'"is_verified":(true|false)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, html)
            if match:
                value = match.group(1)
                if key in ['followers', 'following', 'posts_count']:
                    profile_data[key] = int(value)
                elif key in ['is_private', 'is_verified']:
                    profile_data[key] = value == 'true'
                else:
                    profile_data[key] = value
        
        return profile_data
        
    except Exception as e:
        return {"error": str(e), "method": "requests"}

def scrape_instagram_profile(username, max_posts=5):
    """
    Main function that tries multiple methods
    """
    print(f"🔍 Scraping Instagram profile: @{username}")
    print("=" * 50)
    
    # Try Method 1: Instaloader (more features but may be blocked)
    result1 = scrape_with_instaloader(username, max_posts)
    
    if "error" not in result1:
        print("✅ Method 1 (Instaloader) succeeded!")
        return result1
    else:
        print(f"❌ Method 1 failed: {result1['error']}")
    
    # Try Method 2: HTTP requests (more reliable but limited)
    print("\n🔄 Trying alternative method...")
    result2 = scrape_with_requests(username)
    
    if "error" not in result2:
        print("✅ Method 2 (HTTP requests) succeeded!")
        return result2
    else:
        print(f"❌ Method 2 failed: {result2['error']}")
    
    # Both methods failed
    return {
        "error": "All methods failed",
        "details": {
            "method1_error": result1.get("error"),
            "method2_error": result2.get("error")
        }
    }

def display_results(result):
    """
    Display the scraping results in a nice format
    """
    if "error" in result:
        print(f"\n❌ SCRAPING FAILED")
        print(f"Error: {result['error']}")
        if "details" in result:
            print("\nDetailed errors:")
            for method, error in result["details"].items():
                print(f"  • {method}: {error}")
        
        print("\n🔧 Troubleshooting tips:")
        print("• Make sure the username exists and is spelled correctly")
        print("• Ensure the profile is PUBLIC")
        print("• Wait 10-15 minutes if you see rate limiting errors")
        print("• Try using a VPN if your IP is blocked")
        print("• Check your internet connection")
        return
    
    print(f"\n✅ Successfully scraped profile: @{result['username']}")
    print(f"📊 Method used: {result.get('method', 'Unknown')}")
    print("=" * 50)
    
    # Display profile info
    print(f"👤 Full Name: {result.get('full_name', 'N/A')}")
    
    followers = result.get('followers', 'N/A')
    if isinstance(followers, int):
        print(f"👥 Followers: {followers:,}")
    else:
        print(f"👥 Followers: {followers}")
    
    following = result.get('following', 'N/A')
    if isinstance(following, int):
        print(f"➡️  Following: {following:,}")
    else:
        print(f"➡️  Following: {following}")
    
    posts_count = result.get('posts_count', 'N/A')
    if isinstance(posts_count, int):
        print(f"📸 Posts: {posts_count:,}")
    else:
        print(f"📸 Posts: {posts_count}")
    
    print(f"🔒 Private: {'Yes' if result.get('is_private') else 'No'}")
    print(f"✅ Verified: {'Yes' if result.get('is_verified') else 'No'}")
    
    bio = result.get('biography', 'N/A')
    if bio and bio != 'N/A':
        print(f"📝 Bio: {bio}")
    
    external_url = result.get('external_url', 'N/A')
    if external_url and external_url != 'N/A':
        print(f"🔗 Website: {external_url}")
    
    # Display posts if available
    posts = result.get('posts', [])
    if posts:
        print(f"\n📸 Recent Posts ({len(posts)}):")
        print("-" * 30)
        for i, post in enumerate(posts, 1):
            print(f"\n📌 Post {i}:")
            print(f"   🔗 URL: {post['url']}")
            print(f"   📅 Date: {post['date']}")
            print(f"   📱 Type: {post['type']}")
            caption = post['caption']
            if len(caption) > 100:
                print(f"   📝 Caption: {caption[:100]}...")
            else:
                print(f"   📝 Caption: {caption}")

def main():
    print("🚀 Instagram Public Profile Scraper (Final Version)")
    print("=" * 55)
    print("📌 Works with PUBLIC profiles only (no login required)")
    print("📌 Uses multiple methods for maximum success rate")
    print()
    
    username = input("Enter Instagram username to scrape: ").strip()
    
    try:
        max_posts_input = input("How many posts to scrape? (default 3, max 10): ").strip()
        max_posts = int(max_posts_input) if max_posts_input else 3
        max_posts = min(max_posts, 10)
    except ValueError:
        max_posts = 3
    
    print(f"\n🎯 Starting scrape for @{username}...")
    
    result = scrape_instagram_profile(username, max_posts)
    display_results(result)

if __name__ == "__main__":
    main()
