#!/usr/bin/env python3
"""
Advanced Instagram Scraper with Browser Automation
This version uses Selenium to simulate a real browser and extract post details
"""

import json
import time
import random
from datetime import datetime
import re
import requests
import csv
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def setup_driver():
    """Setup Chrome driver with stealth options"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Uncomment the next line to run in headless mode (no browser window)
    # chrome_options.add_argument("--headless")

    try:
        # Use webdriver-manager to automatically download and manage ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    except Exception as e:
        print(f"❌ Error setting up Chrome driver: {e}")
        print("💡 Make sure you have Chrome browser installed")
        print("💡 ChromeDriver will be downloaded automatically")
        return None

def scrape_with_selenium(username, max_posts=5, scrape_all=False):
    """Scrape Instagram using Selenium browser automation"""
    print("🤖 Using browser automation (Selenium)...")

    driver = setup_driver()
    if not driver:
        return {"error": "Could not setup browser driver"}

    try:
        url = f"https://www.instagram.com/{username}/"
        print(f"🌐 Opening: {url}")

        driver.get(url)

        # Wait for page to load
        time.sleep(random.uniform(3, 5))

        # Check if profile exists
        if "Page Not Found" in driver.title or "Sorry, this page isn't available" in driver.page_source:
            return {"error": "Profile not found"}

        # Extract profile data
        profile_data = {
            "username": username,
            "full_name": "N/A",
            "biography": "N/A",
            "followers": "N/A",
            "following": "N/A",
            "posts_count": "N/A",
            "is_private": False,
            "is_verified": False,
            "posts": []
        }

        # Get profile info
        try:
            # Full name
            try:
                full_name_element = driver.find_element(By.CSS_SELECTOR, "h2")
                profile_data["full_name"] = full_name_element.text
            except:
                pass

            # Stats (followers, following, posts)
            try:
                stats_elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='followers'], a[href*='following'], span")
                for element in stats_elements:
                    text = element.text
                    if "followers" in element.get_attribute("href") if element.get_attribute("href") else "":
                        profile_data["followers"] = text.replace(",", "")
                    elif "following" in element.get_attribute("href") if element.get_attribute("href") else "":
                        profile_data["following"] = text.replace(",", "")
            except:
                pass

            # Bio
            try:
                bio_element = driver.find_element(By.CSS_SELECTOR, "h1 + div")
                profile_data["biography"] = bio_element.text
            except:
                pass

        except Exception as e:
            print(f"⚠️  Error extracting profile info: {e}")

        # Check if private
        if "This Account is Private" in driver.page_source:
            profile_data["is_private"] = True
            return profile_data

        # Scroll to load posts
        if scrape_all:
            print("📜 Scrolling to load ALL posts (this may take a while)...")
            scroll_count = 0
            last_height = driver.execute_script("return document.body.scrollHeight")

            while scroll_count < 50:  # Limit to prevent infinite scrolling
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(random.uniform(3, 6))

                new_height = driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    print(f"   📍 Reached end of posts after {scroll_count} scrolls")
                    break

                last_height = new_height
                scroll_count += 1

                if scroll_count % 5 == 0:
                    print(f"   📜 Scrolled {scroll_count} times, loading more posts...")
        else:
            print(f"📜 Scrolling to load posts (limited to {max_posts})...")
            for i in range(3):
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(random.uniform(2, 4))

        # Find post links
        if scrape_all:
            print(f"🔍 Looking for ALL post links...")
        else:
            print(f"🔍 Looking for {max_posts} post links...")

        post_links = []

        try:
            # Look for post links
            link_elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='/p/']")

            for link in link_elements:
                href = link.get_attribute("href")
                if href and "/p/" in href:
                    post_links.append(href)

            # Remove duplicates
            post_links = list(dict.fromkeys(post_links))

            if not scrape_all:
                post_links = post_links[:max_posts]

            print(f"📌 Found {len(post_links)} post links")

        except Exception as e:
            print(f"⚠️  Error finding post links: {e}")

        # Extract details from each post
        for i, post_url in enumerate(post_links):
            try:
                print(f"📸 Extracting post {i+1}/{len(post_links)}...")

                # Open post in new tab
                driver.execute_script("window.open('');")
                driver.switch_to.window(driver.window_handles[1])
                driver.get(post_url)

                time.sleep(random.uniform(2, 4))

                post_data = {
                    "url": post_url,
                    "shortcode": post_url.split("/p/")[1].split("/")[0],
                    "date": "Unknown",
                    "caption": "No caption",
                    "likes": "N/A",
                    "comments": "N/A",
                    "type": "Unknown"
                }

                # Extract post details
                try:
                    # Caption
                    caption_selectors = [
                        "article div[data-testid='post-caption'] span",
                        "article h1 + div span",
                        "meta[property='og:description']"
                    ]

                    for selector in caption_selectors:
                        try:
                            if selector.startswith("meta"):
                                caption_element = driver.find_element(By.CSS_SELECTOR, selector)
                                caption = caption_element.get_attribute("content")
                            else:
                                caption_element = driver.find_element(By.CSS_SELECTOR, selector)
                                caption = caption_element.text

                            if caption and len(caption) > 10:
                                post_data["caption"] = caption
                                break
                        except:
                            continue

                    # Likes and comments
                    try:
                        # Look for like count
                        like_elements = driver.find_elements(By.CSS_SELECTOR, "span[data-testid='like-count'], a[href*='liked_by'] span")
                        for element in like_elements:
                            text = element.text
                            if text and any(char.isdigit() for char in text):
                                post_data["likes"] = text
                                break
                    except:
                        pass

                    try:
                        # Look for comment count
                        comment_elements = driver.find_elements(By.CSS_SELECTOR, "span[data-testid='comment-count'], a[href*='comments'] span")
                        for element in comment_elements:
                            text = element.text
                            if text and any(char.isdigit() for char in text):
                                post_data["comments"] = text
                                break
                    except:
                        pass

                    # Post type (video/photo)
                    if "video" in driver.page_source.lower():
                        post_data["type"] = "Video"
                    else:
                        post_data["type"] = "Photo"

                    # Date (from meta tags)
                    try:
                        date_element = driver.find_element(By.CSS_SELECTOR, "time")
                        date_text = date_element.get_attribute("datetime")
                        if date_text:
                            post_data["date"] = date_text
                    except:
                        pass

                except Exception as post_error:
                    print(f"   ⚠️  Error extracting post details: {post_error}")

                profile_data["posts"].append(post_data)
                print(f"   ✅ Extracted: {post_data['type']} - {post_data['caption'][:50]}...")

                # Close tab and switch back
                driver.close()
                driver.switch_to.window(driver.window_handles[0])

                # Random delay between posts
                time.sleep(random.uniform(2, 5))

            except Exception as e:
                print(f"   ❌ Error processing post {i+1}: {e}")
                # Make sure we're back on main tab
                if len(driver.window_handles) > 1:
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                continue

        return profile_data

    except Exception as e:
        return {"error": str(e)}

    finally:
        driver.quit()

def scrape_instagram_advanced(username, max_posts=5, scrape_all=False):
    """Main advanced scraping function"""
    print(f"🚀 Advanced Instagram Scraper for @{username}")
    print("=" * 50)
    print("🤖 This version uses browser automation to extract post details")
    if scrape_all:
        print("🌟 SCRAPING ALL POSTS - This may take 10-30 minutes depending on profile size")
    else:
        print("⏳ This may take a few minutes...")
    print()

    result = scrape_with_selenium(username, max_posts, scrape_all)
    return result

def display_advanced_results(result):
    """Display results with post details"""
    if "error" in result:
        print(f"\n❌ SCRAPING FAILED")
        print(f"Error: {result['error']}")

        if "driver" in result['error'].lower():
            print("\n🔧 ChromeDriver Setup:")
            print("1. Download ChromeDriver: https://chromedriver.chromium.org/")
            print("2. Add ChromeDriver to your PATH")
            print("3. Or install via: pip install webdriver-manager")

        return

    print(f"\n✅ Successfully scraped profile: @{result['username']}")
    print("=" * 50)

    # Profile info
    print(f"👤 Full Name: {result.get('full_name', 'N/A')}")
    print(f"👥 Followers: {result.get('followers', 'N/A')}")
    print(f"➡️  Following: {result.get('following', 'N/A')}")
    print(f"📸 Posts: {result.get('posts_count', 'N/A')}")
    print(f"🔒 Private: {'Yes' if result.get('is_private') else 'No'}")

    bio = result.get('biography', 'N/A')
    if bio and bio != 'N/A':
        print(f"📝 Bio: {bio}")

    # Posts with details
    posts = result.get('posts', [])
    if posts:
        print(f"\n📸 Post Details ({len(posts)}):")
        print("=" * 40)

        for i, post in enumerate(posts, 1):
            print(f"\n📌 POST {i}")
            print(f"   🔗 URL: {post['url']}")
            print(f"   📅 Date: {post['date']}")
            print(f"   📱 Type: {post['type']}")
            print(f"   ❤️  Likes: {post['likes']}")
            print(f"   💬 Comments: {post['comments']}")

            caption = post['caption']
            if len(caption) > 100:
                print(f"   📝 Caption: {caption[:100]}...")
            else:
                print(f"   📝 Caption: {caption}")
    else:
        print(f"\n📸 No posts extracted")

def save_to_csv(result, filename=None):
    """Save results to CSV file"""
    if not filename:
        username = result.get('username', 'unknown')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"instagram_{username}_{timestamp}.csv"

    try:
        posts = result.get('posts', [])
        if not posts:
            print("❌ No posts to save to CSV")
            return None

        # Prepare data for CSV
        csv_data = []
        for i, post in enumerate(posts, 1):
            # Clean the data
            likes = post.get('likes', 'N/A')
            if isinstance(likes, str) and 'likes' in likes:
                likes = likes.replace(' likes', '').replace(',', '')

            comments = post.get('comments', 'N/A')
            if isinstance(comments, str) and 'comments' in comments:
                comments = comments.replace(' comments', '').replace(',', '')

            # Clean caption
            caption = post.get('caption', '').replace('\n', ' ').replace('\r', ' ')
            if len(caption) > 500:  # Limit caption length for CSV
                caption = caption[:500] + "..."

            csv_row = {
                'Post_Number': i,
                'Username': result.get('username', ''),
                'Post_URL': post.get('url', ''),
                'Shortcode': post.get('shortcode', ''),
                'Date': post.get('date', ''),
                'Post_Type': post.get('type', ''),
                'Likes': likes,
                'Comments': comments,
                'Caption': caption,
                'Profile_Full_Name': result.get('full_name', ''),
                'Profile_Followers': result.get('followers', ''),
                'Profile_Following': result.get('following', ''),
                'Profile_Posts_Count': result.get('posts_count', ''),
                'Profile_Is_Private': result.get('is_private', False),
                'Profile_Is_Verified': result.get('is_verified', False),
                'Scraped_At': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            csv_data.append(csv_row)

        # Save to CSV using pandas
        df = pd.DataFrame(csv_data)
        df.to_csv(filename, index=False, encoding='utf-8')

        print(f"\n💾 CSV file saved: {filename}")
        print(f"📊 Exported {len(csv_data)} posts to CSV")
        return filename

    except Exception as e:
        print(f"\n❌ Error saving CSV: {e}")
        return None

def save_to_json(result, filename=None):
    """Save results to JSON file"""
    if not filename:
        username = result.get('username', 'unknown')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"instagram_{username}_{timestamp}.json"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n💾 JSON file saved: {filename}")
        return filename
    except Exception as e:
        print(f"\n❌ Error saving JSON: {e}")
        return None

def main():
    print("🚀 Advanced Instagram Scraper with Browser Automation")
    print("=" * 55)
    print("📌 Extracts detailed post information using browser automation")
    print("📌 Works with public profiles (no login required)")
    print("📌 NEW: CSV export and scrape ALL posts options!")
    print("⚠️  Requires ChromeDriver to be installed")
    print()

    username = input("Enter Instagram username to scrape: ").strip()

    # Ask about scraping all posts
    scrape_all_input = input("Do you want to scrape ALL posts? (y/n, default: n): ").strip().lower()
    scrape_all = scrape_all_input == 'y'

    max_posts = 3  # Default
    if not scrape_all:
        try:
            max_posts_input = input("How many posts to scrape? (default 3, max 20): ").strip()
            max_posts = int(max_posts_input) if max_posts_input else 3
            max_posts = min(max_posts, 20)
        except ValueError:
            max_posts = 3

    # Ask about export format
    export_format = input("Export format - CSV, JSON, or Both? (csv/json/both, default: csv): ").strip().lower()
    if export_format not in ['csv', 'json', 'both']:
        export_format = 'csv'

    if scrape_all:
        print(f"\n🌟 Starting to scrape ALL posts for @{username}...")
        print("⚠️  This may take 10-30 minutes depending on the number of posts!")
        confirm = input("Are you sure you want to continue? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Scraping cancelled.")
            return
    else:
        print(f"\n🎯 Starting to scrape {max_posts} posts for @{username}...")

    result = scrape_instagram_advanced(username, max_posts, scrape_all)
    display_advanced_results(result)

    # Save results
    if "error" not in result:
        print(f"\n💾 Saving results...")

        if export_format in ['csv', 'both']:
            save_to_csv(result)

        if export_format in ['json', 'both']:
            save_to_json(result)

        posts_count = len(result.get('posts', []))
        print(f"\n🎉 Scraping completed! Extracted {posts_count} posts.")

        if scrape_all and posts_count > 0:
            print(f"🌟 Successfully scraped ALL {posts_count} posts from @{username}!")
    else:
        print(f"\n❌ Scraping failed: {result['error']}")

if __name__ == "__main__":
    main()
