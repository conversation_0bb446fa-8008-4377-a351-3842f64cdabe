#!/usr/bin/env python3

from instagram_scraper import scrape_instagram_profile

def test_scraper():
    print("🧪 Testing Instagram Public Profile Scraper")
    print("=" * 45)

    # Test with well-known public profiles
    test_profiles = [
        ("instagram", "Instagram's official account"),
        ("natgeo", "National Geographic"),
        ("nasa", "NASA")
    ]

    for username, description in test_profiles:
        print(f"\n🎯 Testing with @{username} ({description})")
        print("-" * 40)

        results = scrape_instagram_profile(username, max_posts=2)

        if isinstance(results, list):
            print(f"✅ SUCCESS! Scraped {len(results)} posts")
            for i, post in enumerate(results, 1):
                print(f"   📌 Post {i}: {post['type']} from {post['date']}")
                print(f"      Likes: {post['likes']}, Comments: {post['comments']}")
            break  # Stop after first successful test
        else:
            print(f"❌ FAILED: {results}")

    else:
        print("\n🚨 All test profiles failed!")
        print("\n🔧 Possible solutions:")
        print("• Instagram may be blocking requests - try again in 10-15 minutes")
        print("• Your IP might be temporarily blocked")
        print("• Try using a VPN")
        print("• Check your internet connection")

def quick_test(username="instagram"):
    """Quick test function for a specific username"""
    print(f"🚀 Quick test for @{username}")
    results = scrape_instagram_profile(username, max_posts=1)

    if isinstance(results, list):
        print(f"✅ Success! Profile is accessible.")
        return True
    else:
        print(f"❌ Failed: {results}")
        return False

if __name__ == "__main__":
    test_scraper()
