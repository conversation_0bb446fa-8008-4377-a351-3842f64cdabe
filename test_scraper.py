#!/usr/bin/env python3

from instagram_scraper import scrape_instagram_profile

def test_scraper():
    print("Testing Instagram Scraper...")
    print("=" * 40)
    
    # Test with a well-known public profile
    username = "instagram"  # Instagram's official account
    max_posts = 2
    
    print(f"Testing with @{username} (public profile)")
    print(f"Attempting to scrape {max_posts} posts without login...")
    
    results = scrape_instagram_profile(username, max_posts=max_posts)
    
    if isinstance(results, list):
        print(f"\n✅ SUCCESS! Scraped {len(results)} posts")
        for i, post in enumerate(results, 1):
            print(f"\nPost {i}:")
            print(f"  Date: {post['date']}")
            print(f"  Likes: {post['likes']}")
            print(f"  URL: {post['url']}")
            caption = post['caption'][:100] + "..." if len(post['caption']) > 100 else post['caption']
            print(f"  Caption: {caption}")
    else:
        print(f"\n❌ FAILED: {results}")
        
        # Suggest solutions
        print("\n🔧 Possible solutions:")
        print("1. Instagram may be blocking requests - try again later")
        print("2. Try with login credentials")
        print("3. Use a VPN if your IP is blocked")
        print("4. The profile might be private or not exist")

if __name__ == "__main__":
    test_scraper()
