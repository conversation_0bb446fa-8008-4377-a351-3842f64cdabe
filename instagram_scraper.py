import instaloader
from datetime import datetime
import os
from PIL import Image
import pytesseract
import time
import random

def scrape_instagram_profile(username, login_username=None, login_password=None, max_posts=10):
    # Create an instance of In<PERSON>oa<PERSON> with better settings
    L = instaloader.Instaloader(
        download_pictures=False,
        download_videos=False,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False
    )

    # Set user agent to avoid detection
    L.context._session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })

    try:
        # Login if credentials provided
        if login_username and login_password:
            print("Attempting to login...")
            try:
                L.login(login_username, login_password)
                print("Login successful!")
                # Add delay after login
                time.sleep(random.uniform(2, 5))
            except Exception as login_error:
                print(f"Login failed: {login_error}")
                print("Continuing without login (limited access)...")

        # Load the profile with retry logic
        profile = None
        for attempt in range(3):
            try:
                profile = instaloader.Profile.from_username(L.context, username)
                break
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < 2:
                    wait_time = random.uniform(5, 10) * (attempt + 1)
                    print(f"Waiting {wait_time:.1f} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    raise e

        if not profile:
            return "Could not load profile after multiple attempts"

        # Iterate over the profile's posts with limits
        print(f"Scraping posts for {username}... (max {max_posts} posts)")

        posts_data = []
        post_count = 0
        for post in profile.get_posts():
            if post_count >= max_posts:
                break
            try:
                # Add random delay between requests to avoid rate limiting
                delay = random.uniform(1, 3)
                print(f"Processing post {post_count + 1}/{max_posts}... (waiting {delay:.1f}s)")
                time.sleep(delay)

                # Get caption
                caption = post.caption if post.caption else "No caption"

                # Get date
                post_date = post.date.strftime("%Y-%m-%d %H:%M:%S")

                # Get likes count (may fail if not logged in)
                try:
                    likes = post.likes
                except Exception:
                    likes = "N/A (login required)"

                # Get basic post info without downloading images (to avoid issues)
                img_description = "Image analysis disabled to avoid rate limiting"

                # Store post data
                post_data = {
                    "caption": caption,
                    "date": post_date,
                    "likes": likes,
                    "image_description": img_description,
                    "shortcode": post.shortcode,
                    "url": f"https://www.instagram.com/p/{post.shortcode}/"
                }

                posts_data.append(post_data)
                print(f"✓ Scraped post from {post_date}")
                post_count += 1

            except Exception as post_error:
                print(f"Error processing post: {post_error}")
                continue

        return posts_data

    except instaloader.exceptions.ProfileNotExistsException:
        return f"Profile {username} does not exist"
    except instaloader.exceptions.LoginRequiredException:
        return f"Login required to access {username}'s profile. Please provide login credentials."
    except instaloader.exceptions.PrivateProfileNotFollowedException:
        return f"Profile {username} is private and you don't follow them. Login and follow to access."
    except instaloader.exceptions.TooManyRequestsException:
        return f"Too many requests. Instagram is rate limiting. Please wait and try again later."
    except Exception as e:
        error_msg = str(e)
        if "403" in error_msg or "Forbidden" in error_msg:
            return f"Access forbidden. Instagram blocked the request. Try using login credentials or wait before retrying."
        elif "401" in error_msg or "Unauthorized" in error_msg:
            return f"Unauthorized access. Instagram requires login for this profile."
        else:
            return f"An error occurred: {error_msg}"

def main():
    print("Instagram Profile Scraper")
    print("=" * 30)

    username = input("Enter Instagram username to scrape: ").strip()

    # Ask for login credentials (optional)
    use_login = input("Do you want to login to Instagram? (y/n): ").strip().lower()
    login_username = None
    login_password = None

    if use_login == 'y':
        login_username = input("Enter your Instagram username: ").strip()
        login_password = input("Enter your Instagram password: ").strip()

    # Ask for number of posts
    try:
        max_posts = int(input("How many posts to scrape? (default 5): ").strip() or "5")
    except ValueError:
        max_posts = 5

    print(f"\nStarting scrape for @{username}...")
    results = scrape_instagram_profile(username, login_username, login_password, max_posts)

    if isinstance(results, list):
        print(f"\n✓ Successfully scraped {len(results)} posts for @{username}")
        print("=" * 50)

        for i, post in enumerate(results, 1):
            print(f"\n--- Post {i} ---")
            print(f"URL: {post['url']}")
            print(f"Date: {post['date']}")
            print(f"Likes: {post['likes']}")
            caption = post['caption']
            if len(caption) > 150:
                print(f"Caption: {caption[:150]}...")
            else:
                print(f"Caption: {caption}")
            print(f"Shortcode: {post['shortcode']}")
    else:
        print(f"\n❌ Error: {results}")
        print("\nTroubleshooting tips:")
        print("1. Try logging in with your Instagram credentials")
        print("2. Wait a few minutes before trying again")
        print("3. Make sure the username exists and is public")
        print("4. Check your internet connection")

if __name__ == "__main__":
    main()