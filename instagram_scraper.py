import instaloader
from datetime import datetime
import os
from PIL import Image
import pytesseract

def scrape_instagram_profile(username):
    # Create an instance of Instaloader
    L = instaloader.Instaloader()
    
    try:
        # Load the profile
        profile = instaloader.Profile.from_username(L.context, username)
        
        # Iterate over the profile's posts
        print(f"Scraping posts for {username}...")
        
        posts_data = []
        for post in profile.get_posts():
            # Get caption
            caption = post.caption if post.caption else "No caption"
            
            # Get date
            post_date = post.date.strftime("%Y-%m-%d %H:%M:%S")
            
            # Get likes count
            likes = post.likes
            
            # Download image for analysis (temporarily)
            temp_dir = "temp_images"
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = os.path.join(temp_dir, f"temp_{post.shortcode}.jpg")
            
            L.download_pic(temp_path, post.url, post.date_utc)
            
            # Basic image description using OCR (you might want to use a more advanced image analysis API)
            try:
                img = Image.open(temp_path)
                img_text = pytesseract.image_to_string(img)
                img_description = f"Image contains text: {img_text}" if img_text.strip() else "No text detected in image"
            except Exception as e:
                img_description = f"Could not analyze image: {str(e)}"
            
            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            # Store post data
            post_data = {
                "caption": caption,
                "date": post_date,
                "likes": likes,
                "image_description": img_description
            }
            
            posts_data.append(post_data)
            print(f"Scraped post from {post_date}")
        
        return posts_data
            
    except instaloader.exceptions.ProfileNotExistsException:
        return f"Profile {username} does not exist"
    except Exception as e:
        return f"An error occurred: {str(e)}"

if __name__ == "__main__":
    username = input("Enter Instagram username to scrape: ")
    results = scrape_instagram_profile(username)
    
    if isinstance(results, list):
        print(f"\nFound {len(results)} posts for {username}")
        for i, post in enumerate(results, 1):
            print(f"\n--- Post {i} ---")
            print(f"Caption: {post['caption'][:100]}..." if len(post['caption']) > 100 else f"Caption: {post['caption']}")
            print(f"Date: {post['date']}")
            print(f"Likes: {post['likes']}")
            print(f"Image Description: {post['image_description'][:100]}..." if len(post['image_description']) > 100 else f"Image Description: {post['image_description']}")
    else:
        print(results)