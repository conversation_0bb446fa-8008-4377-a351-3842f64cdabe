import instaloader
from datetime import datetime
import time
import random

def scrape_instagram_profile(username, max_posts=5):
    """
    Scrape public Instagram profile data without login

    Args:
        username (str): Instagram username to scrape
        max_posts (int): Maximum number of posts to scrape (default: 5)

    Returns:
        list: List of post data dictionaries or error message string
    """
    print(f"🔍 Scraping public profile: @{username}")

    # Create Instaloader instance optimized for public profiles
    L = instaloader.Instaloader(
        download_pictures=False,
        download_videos=False,
        download_video_thumbnails=False,
        download_geotags=False,
        download_comments=False,
        save_metadata=False,
        compress_json=False,
        request_timeout=30,
        max_connection_attempts=3
    )

    # Set realistic user agent
    L.context._session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    try:
        print("📡 Connecting to Instagram...")

        # Load the profile with enhanced retry logic
        profile = None
        for attempt in range(3):
            try:
                # Add initial delay to avoid immediate blocking
                if attempt > 0:
                    wait_time = random.uniform(3, 8) * attempt
                    print(f"⏳ Waiting {wait_time:.1f} seconds before retry {attempt + 1}...")
                    time.sleep(wait_time)

                profile = instaloader.Profile.from_username(L.context, username)
                print(f"✅ Profile loaded successfully!")
                break

            except Exception as e:
                error_msg = str(e).lower()
                print(f"❌ Attempt {attempt + 1} failed: {e}")

                if "429" in error_msg or "too many requests" in error_msg:
                    if attempt < 2:
                        print("🚫 Rate limited - waiting longer...")
                        time.sleep(random.uniform(10, 20))
                    else:
                        return "Rate limited by Instagram. Please wait 10-15 minutes before trying again."
                elif "403" in error_msg or "forbidden" in error_msg:
                    return "Access forbidden. This might be a private profile or Instagram is blocking requests."
                elif attempt == 2:
                    raise e

        if not profile:
            return "Could not load profile after multiple attempts"

        # Check if profile is private
        if profile.is_private:
            return f"Profile @{username} is private. Only public profiles can be scraped without login."

        # Show profile info
        print(f"📊 Profile Info:")
        print(f"   • Full name: {profile.full_name}")
        print(f"   • Followers: {profile.followers:,}")
        print(f"   • Following: {profile.followees:,}")
        print(f"   • Posts: {profile.mediacount:,}")
        print(f"   • Bio: {profile.biography[:100]}..." if len(profile.biography) > 100 else f"   • Bio: {profile.biography}")

        # Iterate over the profile's posts with limits
        print(f"\n📸 Scraping {max_posts} most recent posts...")

        posts_data = []
        post_count = 0

        for post in profile.get_posts():
            if post_count >= max_posts:
                break

            try:
                # Add random delay between requests to avoid rate limiting
                delay = random.uniform(2, 5)
                print(f"📝 Processing post {post_count + 1}/{max_posts}... (waiting {delay:.1f}s)")
                time.sleep(delay)

                # Get basic post information
                caption = post.caption if post.caption else "No caption"
                post_date = post.date.strftime("%Y-%m-%d %H:%M:%S")

                # Try to get engagement metrics (may not work without login)
                try:
                    likes = post.likes
                    comments = post.comments
                except Exception:
                    likes = "N/A (requires login)"
                    comments = "N/A (requires login)"

                # Get post type
                if post.is_video:
                    post_type = "Video"
                    try:
                        video_duration = post.video_duration
                    except:
                        video_duration = "N/A"
                else:
                    post_type = "Photo"
                    video_duration = None

                # Store post data
                post_data = {
                    "shortcode": post.shortcode,
                    "url": f"https://www.instagram.com/p/{post.shortcode}/",
                    "date": post_date,
                    "type": post_type,
                    "caption": caption,
                    "likes": likes,
                    "comments": comments,
                    "hashtags": [tag for tag in post.caption_hashtags] if post.caption_hashtags else [],
                    "mentions": [mention for mention in post.caption_mentions] if post.caption_mentions else []
                }

                if video_duration:
                    post_data["video_duration"] = video_duration

                posts_data.append(post_data)
                print(f"   ✅ Scraped {post_type.lower()} from {post_date}")
                post_count += 1

            except Exception as post_error:
                print(f"   ❌ Error processing post: {post_error}")
                # Don't break, continue with next post
                continue

        print(f"\n🎉 Successfully scraped {len(posts_data)} posts!")
        return posts_data

    except instaloader.exceptions.ProfileNotExistsException:
        return f"Profile {username} does not exist"
    except instaloader.exceptions.LoginRequiredException:
        return f"Login required to access {username}'s profile. Please provide login credentials."
    except instaloader.exceptions.PrivateProfileNotFollowedException:
        return f"Profile {username} is private and you don't follow them. Login and follow to access."
    except instaloader.exceptions.TooManyRequestsException:
        return f"Too many requests. Instagram is rate limiting. Please wait and try again later."
    except Exception as e:
        error_msg = str(e)
        if "403" in error_msg or "Forbidden" in error_msg:
            return f"Access forbidden. Instagram blocked the request. Try using login credentials or wait before retrying."
        elif "401" in error_msg or "Unauthorized" in error_msg:
            return f"Unauthorized access. Instagram requires login for this profile."
        else:
            return f"An error occurred: {error_msg}"

def main():
    print("🚀 Instagram Public Profile Scraper")
    print("=" * 40)
    print("📌 Note: This scraper works with PUBLIC profiles only (no login required)")
    print()

    username = input("Enter Instagram username to scrape: ").strip()

    # Ask for number of posts
    try:
        max_posts_input = input("How many posts to scrape? (default 5, max 20): ").strip()
        max_posts = int(max_posts_input) if max_posts_input else 5
        max_posts = min(max_posts, 20)  # Limit to 20 to avoid rate limiting
    except ValueError:
        max_posts = 5

    print(f"\n🎯 Starting scrape for @{username} ({max_posts} posts)...")
    print("-" * 50)

    results = scrape_instagram_profile(username, max_posts)

    if isinstance(results, list):
        print(f"\n📋 SCRAPING RESULTS")
        print("=" * 50)

        for i, post in enumerate(results, 1):
            print(f"\n📌 POST {i}")
            print(f"   🔗 URL: {post['url']}")
            print(f"   📅 Date: {post['date']}")
            print(f"   📱 Type: {post['type']}")
            print(f"   ❤️  Likes: {post['likes']}")
            print(f"   💬 Comments: {post['comments']}")

            if post.get('video_duration'):
                print(f"   ⏱️  Duration: {post['video_duration']} seconds")

            # Show hashtags if any
            if post['hashtags']:
                hashtags = ' '.join([f"#{tag}" for tag in post['hashtags'][:5]])  # Show first 5
                print(f"   🏷️  Hashtags: {hashtags}")

            # Show mentions if any
            if post['mentions']:
                mentions = ' '.join([f"@{mention}" for mention in post['mentions'][:3]])  # Show first 3
                print(f"   👥 Mentions: {mentions}")

            # Show caption (truncated)
            caption = post['caption']
            if len(caption) > 100:
                print(f"   📝 Caption: {caption[:100]}...")
            else:
                print(f"   📝 Caption: {caption}")

        print(f"\n✅ Successfully scraped {len(results)} posts from @{username}")

    else:
        print(f"\n❌ SCRAPING FAILED")
        print(f"Error: {results}")
        print("\n🔧 Troubleshooting tips:")
        print("• Make sure the username exists and is spelled correctly")
        print("• Ensure the profile is PUBLIC (private profiles require login)")
        print("• Wait 10-15 minutes if you see rate limiting errors")
        print("• Try with a different username to test")
        print("• Check your internet connection")

if __name__ == "__main__":
    main()