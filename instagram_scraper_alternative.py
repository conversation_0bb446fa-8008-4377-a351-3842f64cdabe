#!/usr/bin/env python3
"""
Alternative Instagram Scraper using different approaches
This version tries multiple methods to avoid Instagram's blocking
"""

import requests
import json
import time
import random
from datetime import datetime
import re

class InstagramScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_profile_info_basic(self, username):
        """
        Get basic profile information using web scraping
        """
        try:
            url = f"https://www.instagram.com/{username}/"
            print(f"🌐 Fetching profile page: {url}")
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 404:
                return {"error": f"Profile @{username} not found"}
            elif response.status_code != 200:
                return {"error": f"HTTP {response.status_code}: Could not access profile"}
            
            html = response.text
            
            # Extract basic info from HTML
            profile_info = {}
            
            # Try to find JSON data in the HTML
            json_pattern = r'window\._sharedData\s*=\s*({.*?});'
            match = re.search(json_pattern, html)
            
            if match:
                try:
                    shared_data = json.loads(match.group(1))
                    user_data = shared_data.get('entry_data', {}).get('ProfilePage', [{}])[0].get('graphql', {}).get('user', {})
                    
                    if user_data:
                        profile_info = {
                            'username': user_data.get('username', username),
                            'full_name': user_data.get('full_name', 'N/A'),
                            'biography': user_data.get('biography', 'N/A'),
                            'followers': user_data.get('edge_followed_by', {}).get('count', 'N/A'),
                            'following': user_data.get('edge_follow', {}).get('count', 'N/A'),
                            'posts_count': user_data.get('edge_owner_to_timeline_media', {}).get('count', 'N/A'),
                            'is_private': user_data.get('is_private', True),
                            'profile_pic_url': user_data.get('profile_pic_url_hd', 'N/A'),
                            'external_url': user_data.get('external_url', 'N/A'),
                            'is_verified': user_data.get('is_verified', False)
                        }
                        
                        return profile_info
                except json.JSONDecodeError:
                    pass
            
            # Fallback: Extract basic info from HTML using regex
            profile_info['username'] = username
            
            # Extract follower count
            followers_match = re.search(r'"edge_followed_by":{"count":(\d+)}', html)
            if followers_match:
                profile_info['followers'] = int(followers_match.group(1))
            
            # Extract following count
            following_match = re.search(r'"edge_follow":{"count":(\d+)}', html)
            if following_match:
                profile_info['following'] = int(following_match.group(1))
            
            # Extract posts count
            posts_match = re.search(r'"edge_owner_to_timeline_media":{"count":(\d+)}', html)
            if posts_match:
                profile_info['posts_count'] = int(posts_match.group(1))
            
            # Extract full name
            name_match = re.search(r'"full_name":"([^"]*)"', html)
            if name_match:
                profile_info['full_name'] = name_match.group(1)
            
            # Extract bio
            bio_match = re.search(r'"biography":"([^"]*)"', html)
            if bio_match:
                profile_info['biography'] = bio_match.group(1)
            
            # Check if private
            private_match = re.search(r'"is_private":(true|false)', html)
            if private_match:
                profile_info['is_private'] = private_match.group(1) == 'true'
            
            return profile_info
            
        except requests.RequestException as e:
            return {"error": f"Network error: {str(e)}"}
        except Exception as e:
            return {"error": f"Unexpected error: {str(e)}"}
    
    def scrape_profile(self, username):
        """
        Main scraping function
        """
        print(f"🔍 Scraping Instagram profile: @{username}")
        print("-" * 40)
        
        # Add random delay to avoid detection
        delay = random.uniform(1, 3)
        print(f"⏳ Initial delay: {delay:.1f} seconds")
        time.sleep(delay)
        
        profile_info = self.get_profile_info_basic(username)
        
        if 'error' in profile_info:
            return profile_info
        
        return profile_info

def main():
    print("🚀 Instagram Profile Scraper (Alternative Method)")
    print("=" * 50)
    print("📌 This version extracts basic profile information only")
    print("📌 Works with public profiles without login")
    print()
    
    username = input("Enter Instagram username to scrape: ").strip()
    
    scraper = InstagramScraper()
    result = scraper.scrape_profile(username)
    
    if 'error' in result:
        print(f"\n❌ Error: {result['error']}")
        print("\n🔧 Troubleshooting:")
        print("• Make sure the username is correct")
        print("• Check your internet connection")
        print("• Try again in a few minutes")
    else:
        print(f"\n✅ Successfully scraped profile: @{username}")
        print("=" * 40)
        print(f"👤 Full Name: {result.get('full_name', 'N/A')}")
        print(f"👥 Followers: {result.get('followers', 'N/A'):,}" if isinstance(result.get('followers'), int) else f"👥 Followers: {result.get('followers', 'N/A')}")
        print(f"➡️  Following: {result.get('following', 'N/A'):,}" if isinstance(result.get('following'), int) else f"➡️  Following: {result.get('following', 'N/A')}")
        print(f"📸 Posts: {result.get('posts_count', 'N/A'):,}" if isinstance(result.get('posts_count'), int) else f"📸 Posts: {result.get('posts_count', 'N/A')}")
        print(f"🔒 Private: {'Yes' if result.get('is_private') else 'No'}")
        print(f"✅ Verified: {'Yes' if result.get('is_verified') else 'No'}")
        
        bio = result.get('biography', 'N/A')
        if bio and bio != 'N/A':
            print(f"📝 Bio: {bio}")
        
        external_url = result.get('external_url', 'N/A')
        if external_url and external_url != 'N/A':
            print(f"🔗 Website: {external_url}")

if __name__ == "__main__":
    main()
