#!/usr/bin/env python3
"""
Demo script to show the enhanced Instagram scraper features
"""

from instagram_scraper_advanced import scrape_instagram_advanced, save_to_csv, save_to_json

def demo_limited_scrape():
    """Demo: Scrape limited number of posts"""
    print("🎯 DEMO: Limited Posts Scraping")
    print("=" * 40)
    
    username = "sofien_borsali"
    max_posts = 3
    
    print(f"Scraping {max_posts} posts from @{username}...")
    result = scrape_instagram_advanced(username, max_posts, scrape_all=False)
    
    if "error" not in result:
        posts_count = len(result.get('posts', []))
        print(f"✅ Successfully scraped {posts_count} posts!")
        
        # Save as CSV
        csv_file = save_to_csv(result, f"demo_limited_{username}.csv")
        print(f"📊 CSV saved: {csv_file}")
        
        return result
    else:
        print(f"❌ Error: {result['error']}")
        return None

def demo_all_posts_scrape():
    """Demo: Scrape all posts (use with caution - takes long time)"""
    print("🌟 DEMO: ALL Posts Scraping")
    print("=" * 40)
    print("⚠️  WARNING: This will scrape ALL posts and may take 10-30 minutes!")
    
    username = input("Enter username for ALL posts demo (or press Enter to skip): ").strip()
    if not username:
        print("⏭️  Skipping ALL posts demo")
        return None
    
    confirm = input(f"Are you sure you want to scrape ALL posts from @{username}? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Demo cancelled")
        return None
    
    print(f"🚀 Starting to scrape ALL posts from @{username}...")
    result = scrape_instagram_advanced(username, max_posts=0, scrape_all=True)
    
    if "error" not in result:
        posts_count = len(result.get('posts', []))
        print(f"✅ Successfully scraped ALL {posts_count} posts!")
        
        # Save as both CSV and JSON
        csv_file = save_to_csv(result, f"demo_all_{username}.csv")
        json_file = save_to_json(result, f"demo_all_{username}.json")
        
        print(f"📊 CSV saved: {csv_file}")
        print(f"📄 JSON saved: {json_file}")
        
        return result
    else:
        print(f"❌ Error: {result['error']}")
        return None

def show_csv_preview(csv_filename):
    """Show a preview of the CSV file"""
    try:
        import pandas as pd
        df = pd.read_csv(csv_filename)
        
        print(f"\n📊 CSV Preview: {csv_filename}")
        print("=" * 50)
        print(f"Total rows: {len(df)}")
        print(f"Columns: {', '.join(df.columns)}")
        print("\nFirst 3 rows:")
        print(df.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")

def main():
    print("🚀 Instagram Scraper Demo")
    print("=" * 30)
    print("This demo shows the enhanced features:")
    print("✅ CSV export")
    print("✅ Scrape all posts option")
    print("✅ Detailed post information")
    print()
    
    # Demo 1: Limited scraping
    result1 = demo_limited_scrape()
    
    if result1:
        show_csv_preview(f"demo_limited_sofien_borsali.csv")
    
    print("\n" + "="*50)
    
    # Demo 2: All posts scraping (optional)
    demo_all_posts_scrape()
    
    print("\n🎉 Demo completed!")
    print("\n📋 Summary of features:")
    print("• ✅ Extract post details (likes, dates, captions)")
    print("• ✅ Export to CSV format")
    print("• ✅ Export to JSON format")
    print("• ✅ Scrape limited number of posts")
    print("• ✅ Scrape ALL posts from a profile")
    print("• ✅ Works with public profiles (no login required)")

if __name__ == "__main__":
    main()
