#!/usr/bin/env python3

from instagram_scraper_final import scrape_with_requests

def test_method2():
    print("🧪 Testing Method 2 (HTTP Requests) for Post Extraction")
    print("=" * 55)
    
    username = "sofien_borsali"
    max_posts = 3
    
    print(f"Testing with @{username}")
    result = scrape_with_requests(username, max_posts)
    
    if "error" in result:
        print(f"❌ Failed: {result['error']}")
        return
    
    print(f"✅ Success! Method: {result['method']}")
    print(f"👤 Full Name: {result.get('full_name', 'N/A')}")
    print(f"👥 Followers: {result.get('followers', 'N/A')}")
    print(f"📸 Posts Count: {result.get('posts_count', 'N/A')}")
    
    posts = result.get('posts', [])
    print(f"\n📸 Extracted {len(posts)} posts:")
    
    for i, post in enumerate(posts, 1):
        print(f"\n📌 Post {i}:")
        print(f"   🔗 URL: {post['url']}")
        print(f"   📅 Date: {post['date']}")
        print(f"   📱 Type: {post['type']}")
        print(f"   ❤️  Likes: {post.get('likes', 'N/A')}")
        print(f"   💬 Comments: {post.get('comments', 'N/A')}")
        caption = post['caption'][:100] + "..." if len(post['caption']) > 100 else post['caption']
        print(f"   📝 Caption: {caption}")

if __name__ == "__main__":
    test_method2()
