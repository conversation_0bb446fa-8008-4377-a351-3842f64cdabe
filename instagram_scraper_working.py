#!/usr/bin/env python3
"""
Instagram Public Profile Scraper - Working Version
Focuses on reliable data extraction with current Instagram structure
"""

import instaloader
import requests
import json
import time
import random
from datetime import datetime
import re

def get_profile_data_only(username):
    """
    Get profile data using instaloader (most reliable for profile info)
    """
    print("🔧 Getting profile data...")
    
    try:
        L = instaloader.Instaloader(
            download_pictures=False,
            download_videos=False,
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=False,
            save_metadata=False,
            compress_json=False,
            request_timeout=15,
            max_connection_attempts=2
        )
        
        # Set user agent
        L.context._session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Load profile
        profile = instaloader.Profile.from_username(L.context, username)
        
        if profile.is_private:
            return {"error": "Profile is private"}
        
        # Get profile info (this usually works)
        profile_data = {
            "username": profile.username,
            "full_name": profile.full_name,
            "biography": profile.biography,
            "followers": profile.followers,
            "following": profile.followees,
            "posts_count": profile.mediacount,
            "is_private": profile.is_private,
            "is_verified": profile.is_verified,
            "external_url": profile.external_url,
            "profile_pic_url": profile.profile_pic_url
        }
        
        return profile_data
        
    except Exception as e:
        return {"error": str(e)}

def get_recent_post_urls(username, max_posts=5):
    """
    Try to get recent post URLs using web scraping
    """
    print(f"🔍 Searching for recent post URLs...")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        url = f"https://www.instagram.com/{username}/"
        response = session.get(url, timeout=15)
        
        if response.status_code != 200:
            return []
        
        html = response.text
        
        # Try multiple patterns to find post shortcodes
        patterns = [
            r'"shortcode":"([A-Za-z0-9_-]+)"',
            r'/p/([A-Za-z0-9_-]+)/',
            r'instagram\.com/p/([A-Za-z0-9_-]+)',
        ]
        
        all_shortcodes = []
        for pattern in patterns:
            shortcodes = re.findall(pattern, html)
            all_shortcodes.extend(shortcodes)
        
        # Remove duplicates while preserving order
        unique_shortcodes = []
        seen = set()
        for code in all_shortcodes:
            if code not in seen and len(code) > 5:  # Instagram shortcodes are typically longer
                unique_shortcodes.append(code)
                seen.add(code)
        
        # Limit to max_posts
        unique_shortcodes = unique_shortcodes[:max_posts]
        
        posts = []
        for shortcode in unique_shortcodes:
            post_url = f"https://www.instagram.com/p/{shortcode}/"
            posts.append({
                "shortcode": shortcode,
                "url": post_url,
                "date": "Unknown",
                "caption": "Visit URL to see details",
                "type": "Unknown",
                "likes": "Visit URL to see",
                "comments": "Visit URL to see"
            })
        
        print(f"   📌 Found {len(posts)} post URLs")
        return posts
        
    except Exception as e:
        print(f"   ❌ Error finding posts: {e}")
        return []

def scrape_instagram_profile(username, max_posts=5):
    """
    Main scraping function - focuses on reliable data extraction
    """
    print(f"🔍 Scraping Instagram profile: @{username}")
    print("=" * 50)
    
    # Get profile data (most reliable)
    profile_data = get_profile_data_only(username)
    
    if "error" in profile_data:
        return profile_data
    
    print("✅ Profile data extracted successfully!")
    
    # Try to get post URLs
    posts = get_recent_post_urls(username, max_posts)
    profile_data["posts"] = posts
    
    return profile_data

def display_results(result):
    """
    Display the scraping results
    """
    if "error" in result:
        print(f"\n❌ SCRAPING FAILED")
        print(f"Error: {result['error']}")
        
        print("\n🔧 Troubleshooting tips:")
        print("• Make sure the username exists and is spelled correctly")
        print("• Ensure the profile is PUBLIC")
        print("• Wait 10-15 minutes if you see rate limiting errors")
        print("• Try using a VPN if your IP is blocked")
        print("• Check your internet connection")
        return
    
    print(f"\n✅ Successfully scraped profile: @{result['username']}")
    print("=" * 50)
    
    # Display profile info
    print(f"👤 Full Name: {result.get('full_name', 'N/A')}")
    print(f"👥 Followers: {result.get('followers', 'N/A'):,}")
    print(f"➡️  Following: {result.get('following', 'N/A'):,}")
    print(f"📸 Posts: {result.get('posts_count', 'N/A'):,}")
    print(f"🔒 Private: {'Yes' if result.get('is_private') else 'No'}")
    print(f"✅ Verified: {'Yes' if result.get('is_verified') else 'No'}")
    
    bio = result.get('biography', 'N/A')
    if bio and bio != 'N/A':
        print(f"📝 Bio: {bio}")
    
    external_url = result.get('external_url', 'N/A')
    if external_url and external_url != 'N/A':
        print(f"🔗 Website: {external_url}")
    
    # Display posts
    posts = result.get('posts', [])
    if posts:
        print(f"\n📸 Recent Posts ({len(posts)}):")
        print("-" * 40)
        print("💡 Note: Due to Instagram's restrictions, detailed post data")
        print("   requires visiting individual post URLs")
        
        for i, post in enumerate(posts, 1):
            print(f"\n📌 POST {i}")
            print(f"   🔗 URL: {post['url']}")
            print(f"   📱 Shortcode: {post['shortcode']}")
            print(f"   💡 Visit the URL above to see:")
            print(f"      • Post date and time")
            print(f"      • Full caption")
            print(f"      • Likes and comments count")
            print(f"      • Post type (photo/video)")
    else:
        print(f"\n📸 Posts: Could not extract post URLs")
        print("   💡 This can happen due to Instagram's anti-bot measures")
        print("   💡 Profile information is still available above")
        print("   💡 You can manually visit the profile to see posts")

def save_results_to_file(result, filename=None):
    """
    Save results to a JSON file
    """
    if not filename:
        username = result.get('username', 'unknown')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"instagram_{username}_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Results saved to: {filename}")
        return filename
    except Exception as e:
        print(f"\n❌ Could not save to file: {e}")
        return None

def main():
    print("🚀 Instagram Public Profile Scraper (Working Version)")
    print("=" * 55)
    print("📌 Works with PUBLIC profiles only (no login required)")
    print("📌 Extracts reliable profile data + post URLs")
    print()
    
    username = input("Enter Instagram username to scrape: ").strip()
    
    try:
        max_posts_input = input("How many post URLs to find? (default 5, max 12): ").strip()
        max_posts = int(max_posts_input) if max_posts_input else 5
        max_posts = min(max_posts, 12)
    except ValueError:
        max_posts = 5
    
    save_to_file = input("Save results to JSON file? (y/n): ").strip().lower() == 'y'
    
    print(f"\n🎯 Starting scrape for @{username}...")
    
    result = scrape_instagram_profile(username, max_posts)
    display_results(result)
    
    if save_to_file and "error" not in result:
        save_results_to_file(result)
    
    print(f"\n🎉 Scraping completed!")

if __name__ == "__main__":
    main()
